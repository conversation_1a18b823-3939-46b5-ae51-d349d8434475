"use client";
import {
  createContext,
  useContext,
  useState,
  useEffect,
  type ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { useLoginMutation } from "@/modules/auth/mutations/login-mutation";
import User, { LoginResponse } from "@/types/user";

type AuthContextType = {
  user: User | null;
  login: (email: string, password: string) => void;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
  isInitializing: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Simple function to check if accessToken cookie exists
const hasAuthCookie = () => {
  if (typeof document === 'undefined') return false;
  return document.cookie.includes('accessToken=');
};

// Function to decode JWT and get user data (client-side)
const getUserFromToken = () => {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('accessToken='));
  
  if (!tokenCookie) return null;
  
  try {
    const token = tokenCookie.split('=')[1];
    const payload = JSON.parse(atob(token.split('.')[1]));
    
    // Check if token is expired
    if (payload.exp * 1000 < Date.now()) {
      return null;
    }
    
    // Return user data from token
    return {
      id: payload.sub,
      email: payload.email,
      // Add other fields from your token
    };
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const router = useRouter();

  // Check if user is authenticated on app load
  useEffect(() => {
    const checkAuth = () => {
      if (hasAuthCookie()) {
        const userData = getUserFromToken();
        setUser(userData);
      }
      setIsInitializing(false);
    };

    checkAuth();
  }, []);

  const handleLoginSuccess = (response: LoginResponse) => {
    console.log("Login successful, response:", response);
    const userData: User = response.data;
    console.log("Setting user data:", userData);
    setUser(userData);
    router.push("/");
  };

  const loginMutation = useLoginMutation(handleLoginSuccess);

  const login = (email: string, password: string) => {
    loginMutation.mutate({
      email,
      password,
    });
  };

  const logout = () => {
    // Clear the cookie
    document.cookie = 'accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    setUser(null);
    router.push("/login");
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        isLoading: loginMutation.isPending,
        isAuthenticated: !!user,
        isInitializing,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}