import { IHero } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function UseDeleteHero() {
    const queryClient = useQueryClient();
    return useMutation<IApiResponse<IHero>, Error, IHero>({
        mutationFn: (data: IHero) =>
            fetch(`https://api.trailandtreknepal.com/home-hero/${data.id}`, {
                method: "DELETE",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['hero'] });
        },
    })

}